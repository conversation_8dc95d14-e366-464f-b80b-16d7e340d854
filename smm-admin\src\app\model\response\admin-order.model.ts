export interface AdminOrder {
  id: number;
  tenantId: string;
  tenantDomain: string;
  userId: number;
  userName: string;
  userEmail: string;
  serviceId: number;
  serviceName: string;
  categoryName: string;
  link: string;
  quantity: number;
  charge: number;
  actualCharge: number;
  status: string;
  createdAt: string;
  updatedAt: string;
  apiProviderName: string;
  currency: string;
}

export interface AdminOrderSearchRequest {
  search?: string;
  tenantId?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  size?: number;
}
