.chat-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  background: var(--primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  z-index: 1000;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  }

  &.active {
    background: #dc3545;
  }

  i {
    color: white;
    font-size: 24px;
  }

  .unread-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
  }
}

.chat-window {
  position: fixed;
  bottom: 100px;
  right: 20px;
  width: 350px;
  height: 500px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  transform: translateY(100%) scale(0.8);
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 999;

  &.open {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

.chat-header {
  padding: 16px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--primary);
  color: white;
  border-radius: 12px 12px 0 0;

  h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
  }

  .chat-actions {
    display: flex;
    gap: 8px;
  }

  .btn-icon {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background 0.2s;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }
  }
}

.chat-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.user-list, .room-list {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.user-list-header, .room-list-header {
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: space-between;

  h5 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
  }

  .btn-back {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    color: #6c757d;

    &:hover {
      color: var(--primary);
    }
  }
}

.user-list-content, .room-list-content {
  flex: 1;
  overflow-y: auto;
}

.user-item, .room-item {
  padding: 12px 16px;
  cursor: pointer;
  transition: background 0.2s;
  border-bottom: 1px solid #f8f9fa;

  &:hover {
    background: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }
}

.user-item {
  display: flex;
  align-items: center;
  gap: 12px;

  .user-avatar {
    position: relative;

    img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      object-fit: cover;
    }

    .online-indicator {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #6c757d;
      border: 2px solid white;

      &.online {
        background: #28a745;
      }
    }
  }

  .user-info {
    flex: 1;

    .user-name {
      font-weight: 500;
      font-size: 14px;
    }

    .user-role {
      font-size: 12px;
      color: #6c757d;
    }
  }
}

.room-item {
  display: flex;
  align-items: center;
  justify-content: space-between;

  &.unread {
    background: #f8f9fa;
    font-weight: 600;
  }

  .room-info {
    flex: 1;

    .room-name {
      font-size: 14px;
      margin-bottom: 4px;
    }

    .last-message {
      font-size: 12px;
      color: #6c757d;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 200px;
    }
  }

  .room-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;

    .room-time {
      font-size: 11px;
      color: #6c757d;
    }

    .unread-count {
      background: var(--primary);
      color: white;
      border-radius: 50%;
      width: 18px;
      height: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      font-weight: bold;
    }
  }
}

.chat-messages {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.messages-header {
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 12px;

  .btn-back {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    color: #6c757d;

    &:hover {
      color: var(--primary);
    }
  }

  .room-title {
    font-weight: 600;
    font-size: 14px;
  }
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message {
  display: flex;

  &.own-message {
    justify-content: flex-end;

    .message-content {
      background: var(--primary);
      color: white;
      border-radius: 18px 18px 4px 18px;
    }
  }

  .message-content {
    max-width: 70%;
    background: #f8f9fa;
    border-radius: 18px 18px 18px 4px;
    padding: 8px 12px;

    .message-header {
      margin-bottom: 4px;

      .sender-name {
        font-size: 11px;
        font-weight: 600;
        color: var(--primary);
      }
    }

    .message-text {
      font-size: 14px;
      line-height: 1.4;
      word-wrap: break-word;
    }

    .message-time {
      font-size: 10px;
      opacity: 0.7;
      margin-top: 4px;
    }
  }
}

.message-input-container {
  padding: 16px;
  border-top: 1px solid #e9ecef;

  .message-input {
    display: flex;
    align-items: flex-end;
    gap: 8px;

    textarea {
      flex: 1;
      border: 1px solid #e9ecef;
      border-radius: 20px;
      padding: 8px 12px;
      resize: none;
      font-size: 14px;
      max-height: 80px;
      min-height: 36px;

      &:focus {
        outline: none;
        border-color: var(--primary);
      }
    }

    .send-button {
      background: var(--primary);
      border: none;
      border-radius: 50%;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: white;
      transition: background 0.2s;

      &:hover:not(:disabled) {
        background: #0056b3;
      }

      &:disabled {
        background: #6c757d;
        cursor: not-allowed;
      }
    }
  }
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 32px;
  color: #6c757d;

  i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  h5 {
    margin: 0 0 8px 0;
    font-size: 16px;
  }

  p {
    margin: 0;
    font-size: 14px;
  }
}
