import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { AdminPanelService } from '../../core/services/admin-panel.service';
import { AdminPanelTenant } from '../../model/response/admin-panel-tenant.model';
import { AdminPanelUser } from '../../model/response/admin-panel-user.model';
import { AdminPanelUserRequest } from '../../model/request/admin-panel-user.model';
import { ToastService } from '../../core/services/toast.service';
import { CurrencyService } from '../../core/services/currency.service';
import { Subscription } from 'rxjs';
import { DomainInfoComponent } from '../popup/domain-info/domain-info.component';
import { OrderTrackingComponent } from './order-tracking/order-tracking.component';

@Component({
  selector: 'app-admin-panel',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconsModule,
    TranslateModule,
    DomainInfoComponent,
    OrderTrackingComponent
  ],
  templateUrl: './admin-panel.component.html',
  styleUrls: ['./admin-panel.component.css']
})
export class AdminPanelComponent implements OnInit, OnDestroy {
  // Make Math available in template
  Math = Math;

  // Tab management
  activeTab: 'panels' | 'users' | 'orders' = 'panels';

  // Panels data
  panels: AdminPanelTenant[] = [];
  panelsSearchTerm: string = '';
  panelsPagination = {
    pageNumber: 0,
    pageSize: 10,
    totalElements: 0,
    totalPages: 0
  };

  // Users data
  users: AdminPanelUser[] = [];
  usersSearchTerm: string = '';
  usersPagination = {
    pageNumber: 0,
    pageSize: 10,
    totalElements: 0,
    totalPages: 0
  };

  // Loading state
  isLoading = false;

  // Add money modal
  showAddMoneyModal = false;
  selectedUser: AdminPanelUser | null = null;
  addMoneyForm = {
    amount: '',
    note: ''
  };

  // Domain info modal
  showDomainInfoModal = false;
  selectedTenantId = '';
  selectedDomain = '';

  private subscriptions: Subscription[] = [];

  constructor(
    private adminPanelService: AdminPanelService,
    private toastService: ToastService,
    private currencyService: CurrencyService
  ) {}

  ngOnInit() {
    // Subscribe to panels data
    this.subscriptions.push(
      this.adminPanelService.panels$.subscribe(panels => {
        this.panels = panels;
      })
    );

    // Subscribe to users data
    this.subscriptions.push(
      this.adminPanelService.users$.subscribe(users => {
        this.users = users;
      })
    );

    // Subscribe to loading state
    this.subscriptions.push(
      this.adminPanelService.loading$.subscribe(loading => {
        this.isLoading = loading;
      })
    );

    // Subscribe to panels pagination
    this.subscriptions.push(
      this.adminPanelService.panelsPagination$.subscribe(pagination => {
        this.panelsPagination = pagination;
      })
    );

    // Subscribe to users pagination
    this.subscriptions.push(
      this.adminPanelService.usersPagination$.subscribe(pagination => {
        this.usersPagination = pagination;
      })
    );

    // Load initial data
    this.loadPanels();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  // Tab management
  switchTab(tab: 'panels' | 'users' | 'orders') {
    this.activeTab = tab;
    if (tab === 'panels') {
      this.loadPanels();
    } else if (tab === 'users') {
      this.loadUsers();
    }
    // Orders tab doesn't need initial loading as it's handled by the component
  }

  // Panels methods
  loadPanels(page: number = 0) {
    this.adminPanelService.getAllPanels(page, this.panelsPagination.pageSize, this.panelsSearchTerm)
      .subscribe({
        error: (error) => {
          console.error('Error loading panels:', error);
          this.toastService.showError('Failed to load panels');
        }
      });
  }

  searchPanels() {
    this.loadPanels(0);
  }

  resetPanelsSearch() {
    this.panelsSearchTerm = '';
    this.loadPanels(0);
  }

  // Users methods
  loadUsers(page: number = 0) {
    this.adminPanelService.getMainTenantUsers(page, this.usersPagination.pageSize, this.usersSearchTerm)
      .subscribe({
        error: (error) => {
          console.error('Error loading users:', error);
          this.toastService.showError('Failed to load users');
        }
      });
  }

  searchUsers() {
    this.loadUsers(0);
  }

  resetUsersSearch() {
    this.usersSearchTerm = '';
    this.loadUsers(0);
  }

  // Add money functionality
  openAddMoneyModal(user: AdminPanelUser) {
    this.selectedUser = user;
    this.addMoneyForm = {
      amount: '',
      note: ''
    };
    this.showAddMoneyModal = true;
  }

  closeAddMoneyModal() {
    this.showAddMoneyModal = false;
    this.selectedUser = null;
    this.addMoneyForm = {
      amount: '',
      note: ''
    };
  }

  // Domain info modal methods
  openDomainInfoModal(tenantId: string, domain: string) {
    this.selectedTenantId = tenantId;
    this.selectedDomain = domain;
    this.showDomainInfoModal = true;
  }

  closeDomainInfoModal() {
    this.showDomainInfoModal = false;
    this.selectedTenantId = '';
    this.selectedDomain = '';
  }

  addMoney() {
    if (!this.selectedUser || !this.addMoneyForm.amount) {
      this.toastService.showError('Please enter a valid amount');
      return;
    }

    const amount = parseFloat(this.addMoneyForm.amount);
    if (isNaN(amount) || amount <= 0) {
      this.toastService.showError('Please enter a valid positive amount');
      return;
    }

    const request: AdminPanelUserRequest = {
      userId: this.selectedUser.id,
      amount: amount,
      source: 'BONUS',
      note: this.addMoneyForm.note || `Added by admin panel for user ${this.selectedUser.userName}`
    };

    this.adminPanelService.addMoneyToUser(request).subscribe({
      next: (transaction) => {
        this.toastService.showSuccess(`Successfully added $${amount} to ${this.selectedUser!.userName}`);
        this.closeAddMoneyModal();
        // Refresh users list
        this.loadUsers(this.usersPagination.pageNumber);
      },
      error: (error) => {
        console.error('Error adding money:', error);
        this.toastService.showError('Failed to add money to user');
      }
    });
  }

  // Pagination methods
  loadPanelsPage(page: number) {
    this.loadPanels(page);
  }

  loadUsersPage(page: number) {
    this.loadUsers(page);
  }

  // Utility methods
  formatBalance(balance: number | undefined): string {
    return '$' + this.currencyService.formatBalance(balance);
  }

  formatDate(dateString: string): string {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  }

  getStatusClass(status: string): string {
    switch (status?.toLowerCase()) {
      case 'active':
      case 'activated':
        return 'bg-green-100 text-green-800';
      case 'suspended':
      case 'deactivated':
        return 'bg-red-100 text-red-800';
      case 'expired':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }
}
