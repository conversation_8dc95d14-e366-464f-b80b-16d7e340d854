package tndung.vnfb.smm.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import tndung.vnfb.smm.dto.chat.*;

import java.util.List;

public interface ChatService {

    /**
     * Create a new chat room
     */
    ChatRoomRes createChatRoom(ChatRoomReq req);

    /**
     * Get chat rooms for current user
     */
    Page<ChatRoomRes> getChatRooms(Pageable pageable);

    /**
     * Get or create direct chat room between two users
     */
    ChatRoomRes getOrCreateDirectChat(Long otherUserId);

    /**
     * Send a message to a chat room
     */
    ChatMessageRes sendMessage(ChatMessageReq req);

    /**
     * Get messages in a chat room
     */
    Page<ChatMessageRes> getMessages(Long chatRoomId, Pageable pageable);

    /**
     * Mark messages as read
     */
    void markAsRead(Long chatRoomId);

    /**
     * Get chat room details
     */
    ChatRoomRes getChatRoom(Long chatRoomId);

    /**
     * Get users available for chat (ADMIN_PANEL users for PANEL users and vice versa)
     */
    List<ChatUserRes> getAvailableUsers();

    /**
     * Delete a chat room
     */
    void deleteChatRoom(Long chatRoomId);
}
