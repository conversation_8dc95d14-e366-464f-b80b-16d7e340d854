package tndung.vnfb.smm.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import tndung.vnfb.smm.dto.chat.ChatRoomReq;
import tndung.vnfb.smm.dto.chat.ChatRoomRes;
import tndung.vnfb.smm.entity.ChatRoom;

@Mapper(componentModel = "spring", 
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        uses = {ChatParticipantMapper.class, ChatMessageMapper.class, GUserMapper.class})
public interface ChatRoomMapper {

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "isActive", constant = "true")
    @Mapping(target = "participants", ignore = true)
    @Mapping(target = "messages", ignore = true)
    @Mapping(target = "creator", ignore = true)
    ChatRoom toEntity(ChatRoomReq req);

    @Mapping(target = "lastMessage", ignore = true)
    @Mapping(target = "unreadCount", ignore = true)
    ChatRoomRes toDTO(ChatRoom entity);
}
