package tndung.vnfb.smm.dto.chat;

import lombok.Data;
import tndung.vnfb.smm.constant.enums.Role;

import java.time.OffsetDateTime;
import java.util.List;

@Data
public class ChatUserRes {
    
    private Long id;
    private String userName;
    private String fullName;
    private String email;
    private String avatar;
    private List<Role> roles;
    private OffsetDateTime lastLoginAt;
    private Boolean isOnline;
}
