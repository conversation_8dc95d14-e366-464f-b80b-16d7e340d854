package tndung.vnfb.smm.dto.chat;

import lombok.Data;
import tndung.vnfb.smm.constant.enums.MessageType;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class ChatMessageReq {
    
    @NotNull(message = "Chat room ID is required")
    private Long chatRoomId;
    
    @NotBlank(message = "Message content is required")
    private String content;
    
    private MessageType messageType = MessageType.TEXT;
    
    private String fileUrl;
    private String fileName;
    private Long fileSize;
}
