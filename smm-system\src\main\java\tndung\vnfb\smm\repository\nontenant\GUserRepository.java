package tndung.vnfb.smm.repository.nontenant;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import tndung.vnfb.smm.dto.request.UserSearchReq;
import tndung.vnfb.smm.entity.GUser;
import tndung.vnfb.smm.repository.tenant.TenantAwareRepository;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Tenant-aware repository for GUser entities
 * All queries include tenant filtering condition
 */
@Repository
public interface GUserRepository extends TenantAwareRepository<GUser, Long> {

    /**
     * Find a user by username, email, or phone with tenant filtering
     * 
     * @param userName The username to search for
     * @param email The email to search for
     * @param phone The phone to search for
     * @return Optional containing the user if found
     */
    @Query("SELECT u FROM GUser u " +
           "JOIN UserTenant ut ON u.id = ut.userId " +
           "WHERE (u.userName = :userName OR u.email = :email OR (:phone IS NULL OR u.phone = :phone)) " +
           "AND ut.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant() }")
    Optional<GUser> findByUserNameOrEmailOrPhone(
            @Param("userName") String userName, 
            @Param("email") String email, 
            @Param("phone") String phone);

    /**
     * Find a user by username with tenant filtering
     * 
     * @param userName The username to search for
     * @return Optional containing the user if found
     */
    @Query("SELECT u FROM GUser u " +
           "JOIN UserTenant ut ON u.id = ut.userId " +
           "WHERE u.userName = :userName " +
           "AND ut.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getSiteTenant()}")
    Optional<GUser> findByUserName(@Param("userName") String userName);

    /**
     * Find a user by email with tenant filtering
     * 
     * @param email The email to search for
     * @return Optional containing the user if found
     */
    @Query("SELECT u FROM GUser u " +
           "JOIN UserTenant ut ON u.id = ut.userId " +
           "WHERE u.email = :email " +
           "AND ut.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()}")
    Optional<GUser> findByEmail(@Param("email") String email);

    /**
     * Find a user by phone with tenant filtering
     * 
     * @param phone The phone to search for
     * @return Optional containing the user if found
     */
    @Query("SELECT u FROM GUser u " +
           "JOIN UserTenant ut ON u.id = ut.userId " +
           "WHERE u.phone = :phone " +
           "AND ut.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()}")
    Optional<GUser> findByPhone(@Param("phone") String phone);

    /**
     * Find a user by API key with tenant filtering
     * 
     * @param apiKey The API key to search for
     * @return Optional containing the user if found
     */
    @Query("SELECT u FROM GUser u WHERE u.apiKey = :apiKey and u.status = tndung.vnfb.smm.constant.enums.CommonStatus.ACTIVATED ")

    Optional<GUser> findByApiKey(@Param("apiKey") String apiKey);

    /**
     * Find users with special price count based on search criteria with tenant filtering
     * 
     * @param searchRequest The search request containing filters
     * @param pageable Pagination information
     * @return Page of user objects with special price counts
     */
    @Query("SELECT u, COUNT(sp) as specialPriceCount " +
           "FROM GUser u " +
           "JOIN UserTenant ut ON u.id = ut.userId " +
           "LEFT JOIN SpecialPrice sp ON sp.user.id = u.id AND sp.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} " +
           "WHERE (:#{#req.keyword} IS NULL OR " +
           "       LOWER(u.userName) LIKE LOWER(CONCAT('%', :#{#req.keyword}, '%')) OR " +
           "       LOWER(u.email) LIKE LOWER(CONCAT('%', :#{#req.keyword}, '%')) OR " +
           "       LOWER(u.phone) LIKE LOWER(CONCAT('%', :#{#req.keyword}, '%'))) " +
           "AND (:#{#req.status} IS NULL OR u.status = :#{#req.status}) " +
           "AND ut.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} " +
           "GROUP BY u.id")
    Page<Object[]> findUsersWithSpecialPriceCount(
            @Param("req") UserSearchReq searchRequest,
            Pageable pageable
    );

    /**
     * Count active users between dates with tenant filtering
     * 
     * @param startDate The start date
     * @param endDate The end date
     * @return Count of active users
     */
    @Query("SELECT COUNT(u) FROM GUser u " +
           "JOIN UserTenant ut ON u.id = ut.userId " +
           "WHERE u.status = 1 " +
           "AND u.lastLoginAt BETWEEN :startDate AND :endDate " +
           "AND ut.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()}")
    Long countActiveUsersBetweenDates(
            @Param("startDate") OffsetDateTime startDate, 
            @Param("endDate") OffsetDateTime endDate);
    
    /**
     * Find all users for the current tenant
     * 
     * @return List of users
     */
    @Query("SELECT u FROM GUser u " +
           "JOIN UserTenant ut ON u.id = ut.userId " +
           "WHERE ut.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()}")
    List<GUser> findAllUsers();
    
    /**
     * Find a user by ID with tenant filtering
     *
     * @param id The user ID
     * @return Optional containing the user if found
     */
    @Query("SELECT u FROM GUser u " +
           "JOIN UserTenant ut ON u.id = ut.userId " +
           "WHERE u.id = :id " +
           "AND ut.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()}")
    Optional<GUser> findById(@Param("id") Long id);

    /**
     * Find users from main tenant
     *
     * @param pageable Pagination information
     * @return Page of users from main tenant
     */
    @Query("SELECT u FROM GUser u " +
           "JOIN UserTenant ut ON u.id = ut.userId " +
           "WHERE ut.tenantId = :#{T(tndung.vnfb.smm.constant.Common).MAIN_TENANT}")
    Page<GUser> findMainTenantUsers(Pageable pageable);

    /**
     * Find users from main tenant with search
     *
     * @param search Search term
     * @param pageable Pagination information
     * @return Page of users from main tenant matching search
     */
    @Query("SELECT u FROM GUser u " +
           "JOIN UserTenant ut ON u.id = ut.userId " +
           "WHERE ut.tenantId = :#{T(tndung.vnfb.smm.constant.Common).MAIN_TENANT} " +
           "AND (LOWER(u.userName) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "     LOWER(u.email) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "     LOWER(u.phone) LIKE LOWER(CONCAT('%', :search, '%')))")
    Page<GUser> findMainTenantUsersWithSearch(@Param("search") String search, Pageable pageable);

    /**
     * Find users by roles containing any of the specified roles
     *
     * @param roles List of roles to search for
     * @return List of users with any of the specified roles
     */
    @Query("SELECT u FROM GUser u " +
           "JOIN UserTenant ut ON u.id = ut.userId " +
           "WHERE ut.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} " +
           "AND u.status = tndung.vnfb.smm.constant.enums.CommonStatus.ACTIVATED " +
           "AND EXISTS (SELECT 1 FROM GUser u2 WHERE u2.id = u.id AND " +
           "            (u2.roles LIKE CONCAT('%', :role1, '%') OR " +
           "             (:role2 IS NOT NULL AND u2.roles LIKE CONCAT('%', :role2, '%'))))")
    List<GUser> findByRolesContainingAny(@Param("role1") String role1, @Param("role2") String role2);


}
