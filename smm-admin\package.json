{"name": "smm-admin", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --proxy-config proxy.conf.js", "start:vite": "ng serve --proxy-config proxy.conf.js --configuration development", "start:json": "ng serve --proxy-config proxy.conf.json", "build": "ng build", "build:no-fonts": "ng build --optimization=false", "build:ssr:no-fonts": "node disable-font-inlining.js && ng build --optimization=false && node dist/smm-admin/server/server.mjs", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:smm-admin": "node dist/smm-admin/server/server.mjs", "dev:ssr": "ng run smm-admin:serve-ssr", "dev:ssr:no-fonts": "node disable-font-inlining.js && ng run smm-admin:serve-ssr", "docker:build": "docker-compose build", "docker:up": "docker-compose up", "docker:prod": "docker-compose -f docker-compose.yml up -d"}, "private": true, "dependencies": {"@angular/animations": "^17.3.0", "@angular/cdk": "^17.3.10", "@angular/common": "^17.3.0", "@angular/compiler": "^17.3.0", "@angular/core": "^17.3.0", "@angular/forms": "^17.3.0", "@angular/platform-browser": "^17.3.0", "@angular/platform-browser-dynamic": "^17.3.0", "@angular/platform-server": "^17.3.0", "@angular/router": "^17.3.0", "@angular/ssr": "^17.3.13", "@auth0/angular-jwt": "^5.2.0", "@fortawesome/angular-fontawesome": "^0.14.0", "@fortawesome/fontawesome-svg-core": "^6.4.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.4.2", "@nguniversal/express-engine": "^7.0.2", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@stomp/stompjs": "^7.1.1", "bootstrap": "^5.3.3", "express": "^4.18.2", "moment": "^2.30.1", "ngx-daterangepicker-material": "^6.0.4", "ngx-pagination": "^6.0.3", "ngx-quill": "^25.0.0", "quill": "^2.0.3", "rxjs": "~7.8.0", "sockjs-client": "^1.6.1", "tslib": "^2.3.0", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^17.3.13", "@angular/cli": "^17.3.13", "@angular/compiler-cli": "^17.3.0", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.19.100", "@types/sockjs-client": "^1.5.4", "autoprefixer": "^10.4.21", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.5.3", "postcss-loader": "^8.1.1", "purgecss": "^7.0.2", "tailwindcss": "^3.4.17", "typescript": "~5.4.2", "vite": "^5.4.19"}}