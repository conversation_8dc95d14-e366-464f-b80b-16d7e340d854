import { Compo<PERSON>, OnInit, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ElementRef } from '@angular/core';
import { ChatService, ChatRoom, ChatMessage, ChatUser, ChatMessageRequest } from '../../../core/services/chat.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-chat',
  templateUrl: './chat.component.html',
  styleUrls: ['./chat.component.scss']
})
export class ChatComponent implements OnInit, OnDestroy {
  @ViewChild('messagesContainer') messagesContainer!: ElementRef;
  @ViewChild('messageInput') messageInput!: ElementRef;

  chatRooms: ChatRoom[] = [];
  selectedRoom: ChatRoom | null = null;
  messages: ChatMessage[] = [];
  availableUsers: ChatUser[] = [];
  newMessage: string = '';
  isLoading = false;
  isChatOpen = false;
  showUserList = false;

  private subscriptions: Subscription[] = [];

  constructor(private chatService: ChatService) {}

  ngOnInit(): void {
    this.loadChatRooms();
    this.loadAvailableUsers();
    this.subscribeToMessages();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.chatService.disconnect();
  }

  private subscribeToMessages(): void {
    const messagesSub = this.chatService.messages$.subscribe(messages => {
      if (this.selectedRoom) {
        this.messages = messages.filter(msg => msg.chat_room_id === this.selectedRoom!.id);
        this.scrollToBottom();
      }
    });
    this.subscriptions.push(messagesSub);

    const roomsSub = this.chatService.chatRooms$.subscribe(rooms => {
      this.chatRooms = rooms;
    });
    this.subscriptions.push(roomsSub);
  }

  loadChatRooms(): void {
    this.isLoading = true;
    const sub = this.chatService.getChatRooms().subscribe({
      next: (response) => {
        if (response.success) {
          this.chatRooms = response.data.content;
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading chat rooms:', error);
        this.isLoading = false;
      }
    });
    this.subscriptions.push(sub);
  }

  loadAvailableUsers(): void {
    const sub = this.chatService.getAvailableUsers().subscribe({
      next: (response) => {
        if (response.success) {
          this.availableUsers = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading available users:', error);
      }
    });
    this.subscriptions.push(sub);
  }

  selectRoom(room: ChatRoom): void {
    this.selectedRoom = room;
    this.showUserList = false;
    this.loadMessages(room.id);
    this.markAsRead(room.id);
  }

  loadMessages(roomId: number): void {
    this.chatService.clearMessages();
    const sub = this.chatService.getMessages(roomId).subscribe({
      next: (response) => {
        if (response.success) {
          this.messages = response.data.content.reverse(); // Reverse to show oldest first
          this.scrollToBottom();
        }
      },
      error: (error) => {
        console.error('Error loading messages:', error);
      }
    });
    this.subscriptions.push(sub);
  }

  sendMessage(): void {
    if (!this.newMessage.trim() || !this.selectedRoom) {
      return;
    }

    const messageRequest: ChatMessageRequest = {
      chat_room_id: this.selectedRoom.id,
      content: this.newMessage.trim(),
      message_type: 'TEXT'
    };

    const sub = this.chatService.sendMessage(messageRequest).subscribe({
      next: (response) => {
        if (response.success) {
          this.newMessage = '';
          this.scrollToBottom();
        }
      },
      error: (error) => {
        console.error('Error sending message:', error);
      }
    });
    this.subscriptions.push(sub);
  }

  startDirectChat(user: ChatUser): void {
    const sub = this.chatService.getOrCreateDirectChat(user.id).subscribe({
      next: (response) => {
        if (response.success) {
          const room = response.data;
          this.selectRoom(room);
          this.loadChatRooms(); // Refresh room list
        }
      },
      error: (error) => {
        console.error('Error creating direct chat:', error);
      }
    });
    this.subscriptions.push(sub);
  }

  markAsRead(roomId: number): void {
    const sub = this.chatService.markAsRead(roomId).subscribe({
      next: () => {
        // Update unread count in room list
        const room = this.chatRooms.find(r => r.id === roomId);
        if (room) {
          room.unread_count = 0;
        }
      },
      error: (error) => {
        console.error('Error marking as read:', error);
      }
    });
    this.subscriptions.push(sub);
  }

  toggleChat(): void {
    this.isChatOpen = !this.isChatOpen;
    if (this.isChatOpen && this.chatRooms.length === 0) {
      this.loadChatRooms();
    }
  }

  toggleUserList(): void {
    this.showUserList = !this.showUserList;
    this.selectedRoom = null;
  }

  onKeyPress(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }

  private scrollToBottom(): void {
    setTimeout(() => {
      if (this.messagesContainer) {
        const element = this.messagesContainer.nativeElement;
        element.scrollTop = element.scrollHeight;
      }
    }, 100);
  }

  getRoomDisplayName(room: ChatRoom): string {
    if (room.type === 'DIRECT' && room.participants) {
      // For direct chats, show the other participant's name
      const otherParticipant = room.participants.find(p => p.user_id !== this.getCurrentUserId());
      return otherParticipant?.user?.user_name || room.name || 'Direct Chat';
    }
    return room.name || 'Group Chat';
  }

  private getCurrentUserId(): number {
    // This should return the current user's ID
    // You might need to get this from your auth service
    return 0; // Placeholder
  }

  formatMessageTime(timestamp: string): string {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  getTotalUnreadCount(): number {
    return this.chatRooms.reduce((total, room) => total + (room.unread_count || 0), 0);
  }
}
