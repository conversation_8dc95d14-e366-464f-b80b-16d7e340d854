package tndung.vnfb.smm.dto.chat;

import lombok.Data;
import tndung.vnfb.smm.constant.enums.ChatRoomType;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ChatRoomReq {
    
    private String name;
    
    @NotNull(message = "Chat room type is required")
    private ChatRoomType type = ChatRoomType.DIRECT;
    
    @NotNull(message = "Participant user IDs are required")
    private List<Long> participantUserIds;
}
