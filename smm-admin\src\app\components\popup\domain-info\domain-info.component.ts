import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';
import { DomainInfo } from '../../../model/response/domain-info.model';
import { AdminPanelService } from '../../../core/services/admin-panel.service';

@Component({
  selector: 'app-domain-info',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    IconsModule
  ],
  templateUrl: './domain-info.component.html',
  styleUrl: './domain-info.component.css'
})
export class DomainInfoComponent implements OnInit {
  @Input() tenantId!: string;
  @Input() domain!: string;
  @Output() close = new EventEmitter<void>();

  domainInfo: DomainInfo | null = null;
  isLoading = false;
  error: string | null = null;

  constructor(private adminPanelService: AdminPanelService) {}

  ngOnInit() {
    this.loadDomainInfo();
  }

  loadDomainInfo() {
    if (!this.tenantId) return;

    this.isLoading = true;
    this.error = null;

    this.adminPanelService.getDomainInfo(this.tenantId).subscribe({
      next: (info) => {
        this.domainInfo = info;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading domain info:', error);
        this.error = 'Failed to load domain information';
        this.isLoading = false;
      }
    });
  }

  onClose() {
    this.close.emit();
  }

  formatCurrency(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 6
    }).format(amount);
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  getDaysRemainingClass(days: number): string {
    if (days < 0) return 'text-red-600';
    if (days <= 7) return 'text-orange-600';
    if (days <= 30) return 'text-yellow-600';
    return 'text-green-600';
  }

  getDaysRemainingText(days: number): string {
    if (days < 0) return 'admin.domain_info.expired';
    return 'admin.domain_info.active';
  }
}
