06-03 02:05:52 [main] INFO  t.v.smm.SMMSystemApplication - Starting SMMSystemApplication using Java 17.0.8 on DESKTOP-BLQEK7P with PID 14940 (E:\DUAN_2025\smm\smm-system\target\classes started by X in E:\DUAN_2025\smm\smm-system)
06-03 02:05:52 [main] INFO  t.v.smm.SMMSystemApplication - The following 1 profile is active: "local"
06-03 02:05:54 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
06-03 02:05:54 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
06-03 02:05:54 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 116 ms. Found 6 JPA repository interfaces.
06-03 02:05:54 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
06-03 02:05:54 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
06-03 02:05:54 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 180 ms. Found 27 JPA repository interfaces.
06-03 02:05:54 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
06-03 02:05:54 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.nontenant.CurrencyRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.nontenant.GUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.nontenant.KeyTokenRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.nontenant.TenantRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.nontenant.UserTenantAccessRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.nontenant.UserTenantRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.ActionLogRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.AffiliateRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.ApiProviderRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.CategoryRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.CommissionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.FavoriteServiceRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.GSvRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.IntegrationRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.LoginHistoryRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.NotificationRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.OrderRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.PanelNotificationRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.PlatformRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.PromotionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.ProviderRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.ReferralRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.ReplyRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.SpecialPriceRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.TenantCurrencySettingsRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.TenantCustomLanguageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.TenantI18nContentRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.TicketRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.TransactionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.UpdateLogRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.UserNotificationRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.VoucherRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.VoucherUsageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 02:05:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 127 ms. Found 0 Redis repository interfaces.
06-03 02:05:57 [main] INFO  o.s.b.w.e.t.TomcatWebServer - Tomcat initialized with port(s): 8095 (http)
06-03 02:05:57 [main] INFO  o.a.c.core.StandardService - Starting service [Tomcat]
06-03 02:05:57 [main] INFO  o.a.c.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
06-03 02:05:57 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
06-03 02:05:57 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4942 ms
06-03 02:05:58 [main] INFO  c.z.hikari.HikariDataSource - HikariPool-1 - Starting...
06-03 02:05:58 [main] INFO  c.z.hikari.HikariDataSource - HikariPool-1 - Start completed.
06-03 02:05:58 [main] INFO  o.h.j.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
06-03 02:05:58 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.15.Final
06-03 02:05:59 [main] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
06-03 02:05:59 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQLDialect
06-03 02:06:00 [main] WARN  o.h.id.UUIDHexGenerator - HHH000409: Using org.hibernate.id.UUIDHexGenerator which does not generate IETF RFC 4122 compliant UUID values; consider using org.hibernate.id.UUIDGenerator instead
06-03 02:06:01 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
06-03 02:06:02 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
06-03 02:06:03 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
06-03 02:06:04 [main] DEBUG t.v.s.c.AuthenticationFilter - Filter 'authenticationTokenFilterBean' configured for use
06-03 02:06:09 [main] INFO  o.s.s.w.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6a51a39d, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2acf3108, org.springframework.security.web.context.SecurityContextPersistenceFilter@6042b613, org.springframework.security.web.header.HeaderWriterFilter@23f4aaeb, org.springframework.web.filter.CorsFilter@62765e11, org.springframework.security.web.authentication.logout.LogoutFilter@185b998d, tndung.vnfb.smm.config.AuthenticationFilter@43e7ca6f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@49bcd90d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@55145dc5, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3d73f2a1, org.springframework.security.web.session.SessionManagementFilter@420dee82, org.springframework.security.web.access.ExceptionTranslationFilter@32bff23d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@18cdacd]
06-03 02:06:10 [main] INFO  o.s.b.w.e.t.TomcatWebServer - Tomcat started on port(s): 8095 (http) with context path '/api'
06-03 02:06:10 [scheduling-1] INFO  tndung.vnfb.smm.cron.OrderJob - Triggered handle run job order status
06-03 02:06:10 [main] INFO  t.v.smm.SMMSystemApplication - Started SMMSystemApplication in 19.795 seconds (JVM running for 25.692)
06-03 02:06:11 [task-3] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"157"]
06-03 02:06:11 [task-2] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"156"]
06-03 02:06:13 [task-3] WARN  t.v.s.s.i.BalanceServiceImpl - Order status does not allow refund: orderId=35, status=PENDING
06-03 02:06:13 [task-2] WARN  t.v.s.s.i.BalanceServiceImpl - Order status does not allow refund: orderId=36, status=IN_PROGRESS
06-03 02:06:13 [task-3] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"147"]
06-03 02:06:13 [task-2] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"147"]
06-03 02:07:40 [scheduling-1] INFO  tndung.vnfb.smm.cron.OrderJob - Triggered handle run job order status
06-03 02:07:41 [task-5] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"156"]
06-03 02:07:41 [task-6] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"157"]
06-03 02:07:41 [task-5] WARN  t.v.s.s.i.BalanceServiceImpl - Order status does not allow refund: orderId=36, status=IN_PROGRESS
06-03 02:07:41 [task-5] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"147"]
06-03 02:07:41 [task-6] WARN  t.v.s.s.i.BalanceServiceImpl - Order status does not allow refund: orderId=35, status=PENDING
06-03 02:07:41 [task-6] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"147"]
06-03 02:09:10 [scheduling-1] INFO  tndung.vnfb.smm.cron.OrderJob - Triggered handle run job order status
06-03 02:09:10 [task-7] WARN  c.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@11afb861 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
06-03 02:09:10 [task-7] WARN  c.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@475769ae (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
06-03 02:09:10 [task-7] WARN  c.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@2bd7ad95 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
06-03 02:09:10 [task-7] WARN  c.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@73eb1898 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
06-03 02:09:10 [task-7] WARN  c.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@4fd9ddf1 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
06-03 02:09:10 [task-7] WARN  c.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@33bfc7bd (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
06-03 02:09:10 [task-7] WARN  c.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@25926f5a (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
06-03 02:09:10 [task-7] WARN  c.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@e169ca8 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
06-03 02:09:10 [task-7] WARN  c.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@f21eece (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
06-03 02:09:10 [task-7] WARN  c.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection org.postgresql.jdbc.PgConnection@42ee52b8 (This connection has been closed.). Possibly consider using a shorter maxLifetime value.
06-03 02:09:11 [task-8] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"156"]
06-03 02:09:11 [task-9] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"157"]
06-03 02:09:11 [task-8] WARN  t.v.s.s.i.BalanceServiceImpl - Order status does not allow refund: orderId=36, status=IN_PROGRESS
06-03 02:09:11 [task-8] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"147"]
06-03 02:09:11 [task-9] WARN  t.v.s.s.i.BalanceServiceImpl - Order status does not allow refund: orderId=35, status=PENDING
06-03 02:09:12 [task-9] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"147"]
06-03 02:10:40 [scheduling-1] INFO  tndung.vnfb.smm.cron.OrderJob - Triggered handle run job order status
06-03 02:10:41 [task-12] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"157"]
06-03 02:10:41 [task-11] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"156"]
06-03 02:10:41 [task-12] WARN  t.v.s.s.i.BalanceServiceImpl - Order status does not allow refund: orderId=35, status=PENDING
06-03 02:10:41 [task-12] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"147"]
06-03 02:10:41 [task-11] WARN  t.v.s.s.i.BalanceServiceImpl - Order status does not allow refund: orderId=36, status=IN_PROGRESS
06-03 02:10:41 [task-11] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"147"]
06-03 02:12:10 [scheduling-1] INFO  tndung.vnfb.smm.cron.OrderJob - Triggered handle run job order status
06-03 02:12:11 [task-14] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"156"]
06-03 02:12:11 [task-15] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"157"]
06-03 02:12:11 [task-14] WARN  t.v.s.s.i.BalanceServiceImpl - Order status does not allow refund: orderId=36, status=IN_PROGRESS
06-03 02:12:11 [task-14] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"147"]
06-03 02:12:11 [task-15] WARN  t.v.s.s.i.BalanceServiceImpl - Order status does not allow refund: orderId=35, status=PENDING
06-03 02:12:11 [task-15] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"147"]
06-03 02:13:40 [scheduling-1] INFO  tndung.vnfb.smm.cron.OrderJob - Triggered handle run job order status
06-03 02:13:41 [task-18] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"157"]
06-03 02:13:41 [task-17] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"156"]
06-03 02:13:41 [task-18] WARN  t.v.s.s.i.BalanceServiceImpl - Order status does not allow refund: orderId=35, status=PENDING
06-03 02:13:41 [task-18] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"147"]
06-03 02:13:41 [task-17] WARN  t.v.s.s.i.BalanceServiceImpl - Order status does not allow refund: orderId=36, status=IN_PROGRESS
06-03 02:13:41 [task-17] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"147"]
06-03 02:15:10 [scheduling-1] INFO  tndung.vnfb.smm.cron.OrderJob - Triggered handle run job order status
06-03 02:15:11 [task-21] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"157"]
06-03 02:15:11 [task-20] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"156"]
06-03 02:15:11 [task-20] WARN  t.v.s.s.i.BalanceServiceImpl - Order status does not allow refund: orderId=36, status=IN_PROGRESS
06-03 02:15:11 [task-21] WARN  t.v.s.s.i.BalanceServiceImpl - Order status does not allow refund: orderId=35, status=PENDING
06-03 02:15:11 [task-20] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"147"]
06-03 02:15:11 [task-21] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"147"]
06-03 02:16:40 [scheduling-1] INFO  tndung.vnfb.smm.cron.OrderJob - Triggered handle run job order status
06-03 02:16:41 [task-24] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"157"]
06-03 02:16:41 [task-23] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"156"]
06-03 02:16:41 [task-24] WARN  t.v.s.s.i.BalanceServiceImpl - Order status does not allow refund: orderId=35, status=PENDING
06-03 02:16:41 [task-24] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"147"]
06-03 02:16:41 [task-23] WARN  t.v.s.s.i.BalanceServiceImpl - Order status does not allow refund: orderId=36, status=IN_PROGRESS
06-03 02:16:41 [task-23] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"147"]
06-03 02:18:10 [scheduling-1] INFO  tndung.vnfb.smm.cron.OrderJob - Triggered handle run job order status
06-03 02:18:11 [task-27] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"157"]
06-03 02:18:11 [task-26] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"156"]
06-03 02:18:11 [task-26] WARN  t.v.s.s.i.BalanceServiceImpl - Order status does not allow refund: orderId=36, status=IN_PROGRESS
06-03 02:18:11 [task-26] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"147"]
06-03 02:18:11 [task-27] WARN  t.v.s.s.i.BalanceServiceImpl - Order status does not allow refund: orderId=35, status=PENDING
06-03 02:18:11 [task-27] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"147"]
06-03 02:18:14 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
06-03 02:18:14 [SpringApplicationShutdownHook] INFO  c.z.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
06-03 02:18:14 [SpringApplicationShutdownHook] INFO  c.z.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
06-03 05:11:48 [main] INFO  t.v.smm.SMMSystemApplication - Starting SMMSystemApplication using Java 17.0.8 on DESKTOP-BLQEK7P with PID 8184 (E:\DUAN_2025\smm\smm-system\target\classes started by X in E:\DUAN_2025\smm\smm-system)
06-03 05:11:48 [main] INFO  t.v.smm.SMMSystemApplication - The following 1 profile is active: "local"
06-03 05:12:13 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
06-03 05:12:13 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
06-03 05:12:14 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 194 ms. Found 6 JPA repository interfaces.
06-03 05:12:14 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
06-03 05:12:14 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
06-03 05:12:14 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 184 ms. Found 27 JPA repository interfaces.
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.nontenant.CurrencyRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.nontenant.GUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.nontenant.KeyTokenRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.nontenant.TenantRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.nontenant.UserTenantAccessRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.nontenant.UserTenantRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.ActionLogRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.AffiliateRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.ApiProviderRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.CategoryRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.CommissionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.FavoriteServiceRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.GSvRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.IntegrationRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.LoginHistoryRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.NotificationRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.OrderRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.PanelNotificationRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.PlatformRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.PromotionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.ProviderRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.ReferralRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.ReplyRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.SpecialPriceRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.TenantCurrencySettingsRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.TenantCustomLanguageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.TenantI18nContentRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.TicketRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.TransactionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.UpdateLogRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.UserNotificationRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.VoucherRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface tndung.vnfb.smm.repository.tenant.VoucherUsageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
06-03 05:12:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 397 ms. Found 0 Redis repository interfaces.
06-03 05:12:21 [main] INFO  o.s.b.w.e.t.TomcatWebServer - Tomcat initialized with port(s): 8095 (http)
06-03 05:12:21 [main] INFO  o.a.c.core.StandardService - Starting service [Tomcat]
06-03 05:12:21 [main] INFO  o.a.c.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
06-03 05:12:22 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
06-03 05:12:22 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 33184 ms
06-03 05:12:24 [main] INFO  c.z.hikari.HikariDataSource - HikariPool-1 - Starting...
06-03 05:12:25 [main] INFO  c.z.hikari.HikariDataSource - HikariPool-1 - Start completed.
06-03 05:12:26 [main] INFO  o.h.j.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
06-03 05:12:26 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.15.Final
06-03 05:12:28 [main] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
06-03 05:12:29 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQLDialect
06-03 05:12:31 [main] WARN  o.h.id.UUIDHexGenerator - HHH000409: Using org.hibernate.id.UUIDHexGenerator which does not generate IETF RFC 4122 compliant UUID values; consider using org.hibernate.id.UUIDGenerator instead
06-03 05:12:38 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
06-03 05:12:38 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
06-03 05:12:41 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
06-03 05:12:47 [main] DEBUG t.v.s.c.AuthenticationFilter - Filter 'authenticationTokenFilterBean' configured for use
06-03 05:14:19 [main] INFO  o.s.s.w.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5655d64, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@22ff53d1, org.springframework.security.web.context.SecurityContextPersistenceFilter@3f35d13d, org.springframework.security.web.header.HeaderWriterFilter@46f5030f, org.springframework.web.filter.CorsFilter@568945a9, org.springframework.security.web.authentication.logout.LogoutFilter@2258ca14, tndung.vnfb.smm.config.AuthenticationFilter@74c58f64, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@232e4e1d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3e4d197, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4127dcdb, org.springframework.security.web.session.SessionManagementFilter@47e6004c, org.springframework.security.web.access.ExceptionTranslationFilter@5f7e2ca8, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@713935c8]
06-03 05:14:24 [main] INFO  o.s.b.w.e.t.TomcatWebServer - Tomcat started on port(s): 8095 (http) with context path '/api'
06-03 05:14:24 [scheduling-1] INFO  tndung.vnfb.smm.cron.OrderJob - Triggered handle run job order status
06-03 05:14:24 [main] INFO  t.v.smm.SMMSystemApplication - Started SMMSystemApplication in 157.738 seconds (JVM running for 168.329)
06-03 05:14:30 [task-2] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"156"]
06-03 05:14:30 [task-3] INFO  t.v.s.r.CustomClientHttpRequestInterceptor - Request Headers: [Accept:"application/json, text/html, */*", Content-Type:"application/x-www-form-urlencoded;charset=UTF-8", Content-Length:"157"]
06-03 05:14:34 [task-3] WARN  t.v.s.s.i.BalanceServiceImpl - Order status does not allow refund: orderId=35, status=PENDING
06-03 05:14:34 [task-2] WARN  t.v.s.s.i.BalanceServiceImpl - Order status does not allow refund: orderId=36, status=IN_PROGRESS
06-03 05:14:34 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
06-03 05:14:34 [SpringApplicationShutdownHook] INFO  c.z.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
06-03 05:14:34 [SpringApplicationShutdownHook] INFO  c.z.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
