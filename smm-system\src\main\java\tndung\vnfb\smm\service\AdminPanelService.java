package tndung.vnfb.smm.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import tndung.vnfb.smm.dto.request.AdminPanelUserReq;
import tndung.vnfb.smm.dto.request.AdminOrderSearchReq;
import tndung.vnfb.smm.dto.response.AdminPanelTenantRes;
import tndung.vnfb.smm.dto.response.AdminPanelUserRes;
import tndung.vnfb.smm.dto.response.AdminOrderRes;
import tndung.vnfb.smm.dto.response.DomainInfoRes;
import tndung.vnfb.smm.dto.response.TransactionRes;

public interface AdminPanelService {
    
    /**
     * Get all tenants with main = false (panels)
     */
    Page<AdminPanelTenantRes> getAllPanels(int page, int size, String search);
    
    /**
     * Get all users from main tenant (main = true)
     */
    Page<AdminPanelUserRes> getMainTenantUsers(int page, int size, String search);
    
    /**
     * Add money to a main tenant user
     */
    TransactionRes addMoneyToUser(AdminPanelUserReq request);
    
    /**
     * Get tenant details by ID
     */
    AdminPanelTenantRes getTenantById(String tenantId);
    
    /**
     * Get user details by ID
     */
    AdminPanelUserRes getUserById(Long userId);

    /**
     * Get domain information by tenant ID
     */
    DomainInfoRes getDomainInfo(String tenantId);

    /**
     * Get all orders from all tenants
     */
    Page<AdminOrderRes> getAllOrders(AdminOrderSearchReq searchReq, Pageable pageable);
}
