<!DOCTYPE html>
<html>
<head>
    <title>Test Chat WebSocket</title>
    <script>
        // Fix for sockjs-client
        if (typeof global === 'undefined') {
            var global = globalThis;
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1.6.1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@stomp/stompjs@7.0.0/bundles/stomp.umd.min.js"></script>
</head>
<body>
    <h1>Test Chat WebSocket Connection</h1>
    <div id="status">Connecting...</div>
    <div id="messages"></div>
    
    <script>
        const statusDiv = document.getElementById('status');
        const messagesDiv = document.getElementById('messages');
        
        function log(message) {
            console.log(message);
            messagesDiv.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
        }
        
        try {
            // Test SockJS connection
            const socket = new SockJS('http://localhost:8080/ws');
            
            socket.onopen = function() {
                log('SockJS connection opened');
                statusDiv.textContent = 'Connected to SockJS';
            };
            
            socket.onmessage = function(e) {
                log('SockJS message: ' + e.data);
            };
            
            socket.onclose = function() {
                log('SockJS connection closed');
                statusDiv.textContent = 'Disconnected';
            };
            
            socket.onerror = function(error) {
                log('SockJS error: ' + error);
                statusDiv.textContent = 'Connection error';
            };
            
        } catch (error) {
            log('Error creating SockJS connection: ' + error);
            statusDiv.textContent = 'Failed to connect';
        }
    </script>
</body>
</html>
