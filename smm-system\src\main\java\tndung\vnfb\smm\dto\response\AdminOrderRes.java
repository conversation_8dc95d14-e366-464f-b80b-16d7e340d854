package tndung.vnfb.smm.dto.response;

import lombok.Data;
import tndung.vnfb.smm.constant.enums.OrderStatus;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

@Data
public class AdminOrderRes {
    private Long id;
    private String tenantId;
    private String tenantDomain;
    private Long userId;
    private String userName;
    private String userEmail;
    private Long serviceId;
    private String serviceName;
    private String categoryName;
    private String link;
    private Integer quantity;
    private BigDecimal charge;
    private BigDecimal actualCharge;
    private OrderStatus status;
    private OffsetDateTime createdAt;
    private OffsetDateTime updatedAt;
    private String apiProviderName;
    private String currency;
}
