package tndung.vnfb.smm.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import tndung.vnfb.smm.dto.chat.ChatParticipantRes;
import tndung.vnfb.smm.entity.ChatParticipant;

@Mapper(componentModel = "spring", 
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        uses = {GUserMapper.class})
public interface ChatParticipantMapper {

    ChatParticipantRes toDTO(ChatParticipant entity);
}
