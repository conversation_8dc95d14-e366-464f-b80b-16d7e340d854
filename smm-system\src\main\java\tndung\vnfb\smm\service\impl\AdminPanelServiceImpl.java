package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tndung.vnfb.smm.dto.request.AdminPanelUserReq;
import tndung.vnfb.smm.dto.request.AdminOrderSearchReq;
import tndung.vnfb.smm.dto.response.AdminPanelTenantRes;
import tndung.vnfb.smm.dto.response.AdminPanelUserRes;
import tndung.vnfb.smm.dto.response.AdminOrderRes;
import tndung.vnfb.smm.dto.response.DomainInfoRes;
import tndung.vnfb.smm.dto.response.TransactionRes;
import tndung.vnfb.smm.entity.GUser;
import tndung.vnfb.smm.entity.Tenant;
import tndung.vnfb.smm.exception.IdErrorCode;
import tndung.vnfb.smm.exception.InvalidParameterException;

import tndung.vnfb.smm.repository.nontenant.TenantRepository;
import tndung.vnfb.smm.repository.nontenant.GUserRepository;
import tndung.vnfb.smm.service.AdminPanelService;
import tndung.vnfb.smm.service.BalanceService;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static tndung.vnfb.smm.constant.Common.MAIN_TENANT;

@Service
@RequiredArgsConstructor
@Slf4j
public class AdminPanelServiceImpl implements AdminPanelService {

    private final TenantRepository tenantRepository;
    private final GUserRepository gUserRepository;
    private final BalanceService balanceService;

    @Override
    public Page<AdminPanelTenantRes> getAllPanels(int page, int size, String search) {
        log.info("Getting all panels with search: {}, page: {}, size: {}", search, page, size);
        
        Pageable pageable = PageRequest.of(page, size);
        
        // Get tenants with main = false
        List<Tenant> tenants;
        if (search != null && !search.trim().isEmpty()) {
            tenants = tenantRepository.findByMainFalseAndDomainContainingIgnoreCase(search.trim());
        } else {
            tenants = tenantRepository.findByMainFalse();
        }
        
        // Convert to response DTOs
        List<AdminPanelTenantRes> tenantResponses = tenants.stream()
                .map(this::mapToAdminPanelTenantRes)
                .collect(Collectors.toList());
        
        // Apply pagination manually since we're doing custom filtering
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), tenantResponses.size());
        
        List<AdminPanelTenantRes> pageContent = tenantResponses.subList(start, end);
        
        return new PageImpl<>(pageContent, pageable, tenantResponses.size());
    }

    @Override
    public Page<AdminPanelUserRes> getMainTenantUsers(int page, int size, String search) {
        log.info("Getting main tenant users with search: {}, page: {}, size: {}", search, page, size);
        
        Pageable pageable = PageRequest.of(page, size);
        
        // Get users from main tenant
        Page<GUser> usersPage;
        if (search != null && !search.trim().isEmpty()) {
            usersPage = gUserRepository.findMainTenantUsersWithSearch(search.trim(), pageable);
        } else {
            usersPage = gUserRepository.findMainTenantUsers(pageable);
        }
        
        // Convert to response DTOs
        List<AdminPanelUserRes> userResponses = usersPage.getContent().stream()
                .map(this::mapToAdminPanelUserRes)
                .collect(Collectors.toList());
        
        return new PageImpl<>(userResponses, pageable, usersPage.getTotalElements());
    }

    @Override
    @Transactional
    public TransactionRes addMoneyToUser(AdminPanelUserReq request) {
        log.info("Adding money to user: {}, amount: {}", request.getUserId(), request.getAmount());
        
        // Find the user
        GUser user = gUserRepository.findById(request.getUserId())
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));
        
        // Verify user is from main tenant
        if (!isUserFromMainTenant(user)) {
            throw new InvalidParameterException(IdErrorCode.USER_NOT_FOUND);
        }
        
        // Add balance using BalanceService
        GUser updatedUser = balanceService.addBalance(
                user, 
                request.getAmount(), 
                request.getSource(), 
                request.getNote() != null ? request.getNote() : "Added by admin panel"
        );
        
        // Create a simple transaction response
        // Since we don't have the transaction mapper, we'll create a basic response
        TransactionRes response = new TransactionRes();
        response.setUser(updatedUser.getId().intValue());
        response.setChange(request.getAmount());
        response.setBalance(updatedUser.getBalance());
        response.setNote(request.getNote() != null ? request.getNote() : "Added by admin panel");
        response.setType(tndung.vnfb.smm.constant.enums.TransactionType.Bonus);
        response.setSource(request.getSource());
        response.setCreatedAt(java.time.OffsetDateTime.now());
        return response;
    }

    @Override
    public AdminPanelTenantRes getTenantById(String tenantId) {
        log.info("Getting tenant by ID: {}", tenantId);
        
        Tenant tenant = tenantRepository.findById(tenantId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));
        
        return mapToAdminPanelTenantRes(tenant);
    }

    @Override
    public AdminPanelUserRes getUserById(Long userId) {
        log.info("Getting user by ID: {}", userId);
        
        GUser user = gUserRepository.findById(userId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));
        
        return mapToAdminPanelUserRes(user);
    }

    @Override
    public DomainInfoRes getDomainInfo(String tenantId) {
        log.info("Getting domain info for tenant ID: {}", tenantId);

        Tenant tenant = tenantRepository.findById(tenantId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));

        DomainInfoRes response = new DomainInfoRes();
        response.setId(tenant.getId());
        response.setDomain(tenant.getDomain());
        response.setStatus(tenant.getStatus().toString());
        response.setSubscriptionEndDate(tenant.getSubscriptionEndDate());
        response.setMainCurrency("USD"); // Default currency

        // Calculate days remaining
        if (tenant.getSubscriptionEndDate() != null) {
            long daysRemaining = ChronoUnit.DAYS.between(ZonedDateTime.now(), tenant.getSubscriptionEndDate());
            response.setDaysRemaining(daysRemaining);
        } else {
            response.setDaysRemaining(0L);
        }

        // TODO: Implement actual statistics queries
        response.setTotalUsers(0);
        response.setTotalServices(0);
        response.setTotalOrders(0);
        response.setProviders(new ArrayList<>());
        response.setRevenue(BigDecimal.ZERO);

        return response;
    }

    @Override
    public Page<AdminOrderRes> getAllOrders(AdminOrderSearchReq searchReq, Pageable pageable) {
        log.info("Getting all orders with search criteria: {}", searchReq);

        // TODO: Implement actual order search query
        // For now, return empty page
        List<AdminOrderRes> orders = new ArrayList<>();
        return new PageImpl<>(orders, pageable, 0);
    }

    private AdminPanelTenantRes mapToAdminPanelTenantRes(Tenant tenant) {
        AdminPanelTenantRes response = new AdminPanelTenantRes();
        response.setId(tenant.getId());
        response.setDomain(tenant.getDomain());
        response.setStatus(tenant.getStatus());
        response.setApiUrl(tenant.getApiUrl());
        response.setSiteUrl(tenant.getSiteUrl());
        response.setContactEmail(tenant.getContactEmail());
        response.setMain(tenant.getMain());
        response.setSubscriptionStartDate(tenant.getSubscriptionStartDate());
        response.setSubscriptionEndDate(tenant.getSubscriptionEndDate());
        response.setAutoRenewal(tenant.getAutoRenewal());
        response.setMainCurrency("USD"); // Default currency
        response.setAvailableCurrencies(tenant.getAvailableCurrencies());
        response.setCreatedAt(tenant.getCreatedAt());
        response.setUpdatedAt(tenant.getCreatedAt()); // Use createdAt as updatedAt since updatedAt doesn't exist
        
        // Get tenant owner information and user count
        // This would require additional queries to get the owner and count users
        // For now, we'll set basic info
        response.setTotalUsers(0); // TODO: Implement user count query
        
        return response;
    }

    private AdminPanelUserRes mapToAdminPanelUserRes(GUser user) {
        AdminPanelUserRes response = new AdminPanelUserRes();
        response.setId(user.getId());
        response.setUserName(user.getUserName());
        response.setEmail(user.getEmail());
        response.setPhone(user.getPhone());
        response.setBalance(user.getBalance());
        response.setStatus(user.getStatus());
        response.setTimeZone(user.getTimeZone());
        response.setLanguage(user.getLanguage());
        response.setLastLoginAt(user.getLastLoginAt());
        response.setCreatedAt(user.getCreatedAt());
        response.setUpdatedAt(user.getUpdatedAt());
        
        // Additional info for main tenant users
        response.setTenantId(MAIN_TENANT);
        response.setTenantDomain("main"); // TODO: Get actual main tenant domain
        response.setTotalOrders(0); // TODO: Implement order count query
        response.setTotalSpent(user.getBalance()); // TODO: Implement total spent calculation
        
        return response;
    }

    private boolean isUserFromMainTenant(GUser user) {
        // Check if user belongs to main tenant
        // This would require checking user_tenant table or similar
        // For now, we'll assume users with PANEL role are from main tenant
        return user.getRoles().contains(tndung.vnfb.smm.constant.enums.Role.PANEL);
    }
}
