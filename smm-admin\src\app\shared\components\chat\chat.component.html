<!-- Chat Toggle Button -->
<div class="chat-toggle" [class.active]="isChatOpen" (click)="toggleChat()">
  <i class="fas fa-comments"></i>
  <span class="unread-badge" *ngIf="getTotalUnreadCount() > 0">{{ getTotalUnreadCount() }}</span>
</div>

<!-- Chat Window -->
<div class="chat-window" [class.open]="isChatOpen">
  <div class="chat-header">
    <h4>Live Chat</h4>
    <div class="chat-actions">
      <button class="btn-icon" (click)="toggleUserList()" title="New Chat">
        <i class="fas fa-plus"></i>
      </button>
      <button class="btn-icon" (click)="toggleChat()" title="Close">
        <i class="fas fa-times"></i>
      </button>
    </div>
  </div>

  <div class="chat-body">
    <!-- User List View -->
    <div class="user-list" *ngIf="showUserList">
      <div class="user-list-header">
        <h5>Start New Chat</h5>
        <button class="btn-back" (click)="toggleUserList()">
          <i class="fas fa-arrow-left"></i>
        </button>
      </div>
      <div class="user-list-content">
        <div class="user-item" 
             *ngFor="let user of availableUsers" 
             (click)="startDirectChat(user)">
          <div class="user-avatar">
            <img [src]="user.avatar || '/assets/images/default-avatar.png'" 
                 [alt]="user.user_name">
            <span class="online-indicator" [class.online]="user.is_online"></span>
          </div>
          <div class="user-info">
            <div class="user-name">{{ user.full_name || user.user_name }}</div>
            <div class="user-role">{{ user.roles.join(', ') }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Chat Room List -->
    <div class="room-list" *ngIf="!showUserList && !selectedRoom">
      <div class="room-list-header">
        <h5>Conversations</h5>
      </div>
      <div class="room-list-content">
        <div class="room-item" 
             *ngFor="let room of chatRooms" 
             (click)="selectRoom(room)"
             [class.unread]="room.unread_count && room.unread_count > 0">
          <div class="room-info">
            <div class="room-name">{{ getRoomDisplayName(room) }}</div>
            <div class="last-message" *ngIf="room.last_message">
              {{ room.last_message.content }}
            </div>
          </div>
          <div class="room-meta">
            <div class="room-time" *ngIf="room.last_message">
              {{ formatMessageTime(room.last_message.created_at) }}
            </div>
            <span class="unread-count" *ngIf="room.unread_count && room.unread_count > 0">
              {{ room.unread_count }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Chat Messages View -->
    <div class="chat-messages" *ngIf="selectedRoom && !showUserList">
      <div class="messages-header">
        <button class="btn-back" (click)="selectedRoom = null">
          <i class="fas fa-arrow-left"></i>
        </button>
        <div class="room-title">{{ getRoomDisplayName(selectedRoom) }}</div>
      </div>
      
      <div class="messages-container" #messagesContainer>
        <div class="message" 
             *ngFor="let message of messages"
             [class.own-message]="message.sender_id === getCurrentUserId()">
          <div class="message-content">
            <div class="message-header" *ngIf="message.sender_id !== getCurrentUserId()">
              <span class="sender-name">{{ message.sender?.user_name }}</span>
            </div>
            <div class="message-text">{{ message.content }}</div>
            <div class="message-time">{{ formatMessageTime(message.created_at) }}</div>
          </div>
        </div>
      </div>

      <div class="message-input-container">
        <div class="message-input">
          <textarea #messageInput
                    [(ngModel)]="newMessage"
                    (keydown)="onKeyPress($event)"
                    placeholder="Type a message..."
                    rows="1"></textarea>
          <button class="send-button" 
                  (click)="sendMessage()" 
                  [disabled]="!newMessage.trim()">
            <i class="fas fa-paper-plane"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div class="empty-state" *ngIf="!showUserList && !selectedRoom && chatRooms.length === 0">
      <i class="fas fa-comments"></i>
      <h5>No conversations yet</h5>
      <p>Start a new chat by clicking the + button</p>
    </div>
  </div>
</div>
