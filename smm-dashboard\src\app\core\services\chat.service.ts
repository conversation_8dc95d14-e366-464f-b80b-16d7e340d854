import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { ConfigService } from './config.service';
import { Client } from '@stomp/stompjs';
import SockJS from 'sockjs-client';

export interface ChatRoom {
  id: number;
  name: string;
  type: 'DIRECT' | 'GROUP';
  created_by: number;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  creator?: any;
  participants?: ChatParticipant[];
  last_message?: ChatMessage;
  unread_count?: number;
}

export interface ChatMessage {
  id: number;
  chat_room_id: number;
  sender_id: number;
  content: string;
  message_type: 'TEXT' | 'IMAGE' | 'FILE';
  file_url?: string;
  file_name?: string;
  file_size?: number;
  created_at: string;
  updated_at: string;
  sender?: any;
}

export interface ChatParticipant {
  id: number;
  chat_room_id: number;
  user_id: number;
  joined_at: string;
  last_read_at?: string;
  is_active: boolean;
  user?: any;
}

export interface ChatUser {
  id: number;
  user_name: string;
  full_name: string;
  email: string;
  avatar?: string;
  roles: string[];
  last_login_at?: string;
  is_online: boolean;
}

export interface ChatRoomRequest {
  name?: string;
  type: 'DIRECT' | 'GROUP';
  participant_user_ids: number[];
}

export interface ChatMessageRequest {
  chat_room_id: number;
  content: string;
  message_type?: 'TEXT' | 'IMAGE' | 'FILE';
  file_url?: string;
  file_name?: string;
  file_size?: number;
}

@Injectable({
  providedIn: 'root'
})
export class ChatService {
  private stompClient: Client | null = null;
  private messagesSubject = new BehaviorSubject<ChatMessage[]>([]);
  private chatRoomsSubject = new BehaviorSubject<ChatRoom[]>([]);
  private onlineUsersSubject = new BehaviorSubject<number[]>([]);

  public messages$ = this.messagesSubject.asObservable();
  public chatRooms$ = this.chatRoomsSubject.asObservable();
  public onlineUsers$ = this.onlineUsersSubject.asObservable();

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {
    this.initializeWebSocketConnection();
  }

  private initializeWebSocketConnection(): void {
    const socket = new SockJS(`${this.configService.apiUrl}/ws`);
    this.stompClient = new Client({
      webSocketFactory: () => socket,
      debug: (str) => {
        console.log('STOMP: ' + str);
      },
      reconnectDelay: 5000,
      heartbeatIncoming: 4000,
      heartbeatOutgoing: 4000,
    });

    this.stompClient.onConnect = (frame) => {
      console.log('Connected: ' + frame);
      
      // Subscribe to personal message queue
      this.stompClient?.subscribe('/user/queue/messages', (message) => {
        const chatMessage: ChatMessage = JSON.parse(message.body);
        this.handleNewMessage(chatMessage);
      });

      // Subscribe to public chat updates
      this.stompClient?.subscribe('/topic/public', (message) => {
        const chatMessage: ChatMessage = JSON.parse(message.body);
        this.handleNewMessage(chatMessage);
      });
    };

    this.stompClient.onStompError = (frame) => {
      console.error('Broker reported error: ' + frame.headers['message']);
      console.error('Additional details: ' + frame.body);
    };

    this.stompClient.activate();
  }

  private handleNewMessage(message: ChatMessage): void {
    const currentMessages = this.messagesSubject.value;
    this.messagesSubject.next([...currentMessages, message]);
    
    // Update chat rooms to reflect new message
    this.refreshChatRooms();
  }

  // Chat Rooms API
  getChatRooms(page: number = 0, size: number = 20): Observable<any> {
    return this.http.get(`${this.configService.apiUrl}/chat/rooms?page=${page}&size=${size}`);
  }

  createChatRoom(request: ChatRoomRequest): Observable<ChatRoom> {
    return this.http.post<ChatRoom>(`${this.configService.apiUrl}/chat/rooms`, request);
  }

  getOrCreateDirectChat(userId: number): Observable<ChatRoom> {
    return this.http.post<ChatRoom>(`${this.configService.apiUrl}/chat/rooms/direct/${userId}`, {});
  }

  getChatRoom(roomId: number): Observable<ChatRoom> {
    return this.http.get<ChatRoom>(`${this.configService.apiUrl}/chat/rooms/${roomId}`);
  }

  deleteChatRoom(roomId: number): Observable<void> {
    return this.http.delete<void>(`${this.configService.apiUrl}/chat/rooms/${roomId}`);
  }

  // Messages API
  sendMessage(request: ChatMessageRequest): Observable<ChatMessage> {
    return this.http.post<ChatMessage>(`${this.configService.apiUrl}/chat/messages`, request);
  }

  getMessages(roomId: number, page: number = 0, size: number = 50): Observable<any> {
    return this.http.get(`${this.configService.apiUrl}/chat/rooms/${roomId}/messages?page=${page}&size=${size}`);
  }

  markAsRead(roomId: number): Observable<void> {
    return this.http.post<void>(`${this.configService.apiUrl}/chat/rooms/${roomId}/read`, {});
  }

  // Users API
  getAvailableUsers(): Observable<ChatUser[]> {
    return this.http.get<ChatUser[]>(`${this.configService.apiUrl}/chat/users`);
  }

  // WebSocket methods
  sendMessageViaWebSocket(message: ChatMessageRequest): void {
    if (this.stompClient && this.stompClient.connected) {
      this.stompClient.publish({
        destination: '/app/chat.sendMessage',
        body: JSON.stringify(message)
      });
    }
  }

  disconnect(): void {
    if (this.stompClient) {
      this.stompClient.deactivate();
    }
  }

  // Helper methods
  private refreshChatRooms(): void {
    this.getChatRooms().subscribe(response => {
      if (response.success) {
        this.chatRoomsSubject.next(response.data.content);
      }
    });
  }

  // Get current messages for a specific room
  getMessagesForRoom(roomId: number): ChatMessage[] {
    return this.messagesSubject.value.filter(msg => msg.chat_room_id === roomId);
  }

  // Clear messages (useful when switching rooms)
  clearMessages(): void {
    this.messagesSubject.next([]);
  }
}
