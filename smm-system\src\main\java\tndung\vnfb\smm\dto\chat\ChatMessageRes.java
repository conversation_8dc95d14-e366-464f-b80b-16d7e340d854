package tndung.vnfb.smm.dto.chat;

import lombok.Data;
import tndung.vnfb.smm.constant.enums.MessageType;
import tndung.vnfb.smm.dto.user.GUserRes;

import java.time.OffsetDateTime;

@Data
public class ChatMessageRes {
    
    private Long id;
    private Long chatRoomId;
    private Long senderId;
    private String content;
    private MessageType messageType;
    private String fileUrl;
    private String fileName;
    private Long fileSize;
    private OffsetDateTime createdAt;
    private OffsetDateTime updatedAt;
    
    private GUserRes sender;
}
