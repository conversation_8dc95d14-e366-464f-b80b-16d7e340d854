package tndung.vnfb.smm.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import tndung.vnfb.smm.dto.ApiResponseEntity;
import tndung.vnfb.smm.dto.request.AdminPanelUserReq;
import tndung.vnfb.smm.dto.request.AdminOrderSearchReq;
import tndung.vnfb.smm.dto.response.AdminPanelTenantRes;
import tndung.vnfb.smm.dto.response.AdminPanelUserRes;
import tndung.vnfb.smm.dto.response.AdminOrderRes;
import tndung.vnfb.smm.dto.response.DomainInfoRes;
import tndung.vnfb.smm.dto.response.TransactionRes;
import tndung.vnfb.smm.service.AdminPanelService;

import javax.validation.Valid;

@RestController
@RequestMapping("/v1/admin-panels")
@RequiredArgsConstructor
@Slf4j
public class AdminPanelController {

    private final AdminPanelService adminPanelService;

    /**
     * Get all panels (tenants with main = false)
     */
    @GetMapping("/panels")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<Page<AdminPanelTenantRes>> getAllPanels(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search) {
        
        log.info("Getting all panels - page: {}, size: {}, search: {}", page, size, search);
        Page<AdminPanelTenantRes> panels = adminPanelService.getAllPanels(page, size, search);
        return ApiResponseEntity.success(panels);
    }

    /**
     * Get all users from main tenant
     */
    @GetMapping("/main-tenant-users")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<Page<AdminPanelUserRes>> getMainTenantUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search) {
        
        log.info("Getting main tenant users - page: {}, size: {}, search: {}", page, size, search);
        Page<AdminPanelUserRes> users = adminPanelService.getMainTenantUsers(page, size, search);
        return ApiResponseEntity.success(users);
    }

    /**
     * Add money to a main tenant user
     */
    @PostMapping("/add-money")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<TransactionRes> addMoneyToUser(@RequestBody @Valid AdminPanelUserReq request) {
        log.info("Adding money to user: {}, amount: {}", request.getUserId(), request.getAmount());
        TransactionRes transaction = adminPanelService.addMoneyToUser(request);
        return ApiResponseEntity.success(transaction);
    }

    /**
     * Get tenant details by ID
     */
    @GetMapping("/panels/{tenantId}")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<AdminPanelTenantRes> getTenantById(@PathVariable String tenantId) {
        log.info("Getting tenant by ID: {}", tenantId);
        AdminPanelTenantRes tenant = adminPanelService.getTenantById(tenantId);
        return ApiResponseEntity.success(tenant);
    }

    /**
     * Get user details by ID
     */
    @GetMapping("/users/{userId}")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<AdminPanelUserRes> getUserById(@PathVariable Long userId) {
        log.info("Getting user by ID: {}", userId);
        AdminPanelUserRes user = adminPanelService.getUserById(userId);
        return ApiResponseEntity.success(user);
    }

    /**
     * Get domain information by tenant ID
     */
    @GetMapping("/panels/{tenantId}/info")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<DomainInfoRes> getDomainInfo(@PathVariable String tenantId) {
        log.info("Getting domain info for tenant ID: {}", tenantId);
        DomainInfoRes domainInfo = adminPanelService.getDomainInfo(tenantId);
        return ApiResponseEntity.success(domainInfo);
    }

    /**
     * Get all orders from all tenants
     */
    @GetMapping("/orders")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<Page<AdminOrderRes>> getAllOrders(
            AdminOrderSearchReq searchReq,
            Pageable pageable) {
        log.info("Getting all orders with search criteria: {}", searchReq);
        Page<AdminOrderRes> orders = adminPanelService.getAllOrders(searchReq, pageable);
        return ApiResponseEntity.success(orders);
    }
}
