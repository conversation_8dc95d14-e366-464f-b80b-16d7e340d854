package tndung.vnfb.smm.dto.chat;

import lombok.Data;
import tndung.vnfb.smm.constant.enums.ChatRoomType;
import tndung.vnfb.smm.dto.user.GUserRes;

import java.time.OffsetDateTime;
import java.util.List;

@Data
public class ChatRoomRes {
    
    private Long id;
    private String name;
    private ChatRoomType type;
    private Long createdBy;
    private OffsetDateTime createdAt;
    private OffsetDateTime updatedAt;
    private Boolean isActive;
    
    private GUserRes creator;
    private List<ChatParticipantRes> participants;
    private ChatMessageRes lastMessage;
    private Long unreadCount;
}
