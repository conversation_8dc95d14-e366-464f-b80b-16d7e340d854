package tndung.vnfb.smm.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import tndung.vnfb.smm.entity.ChatMessage;

import java.time.OffsetDateTime;
import java.util.Optional;

@Repository
public interface ChatMessageRepository extends JpaRepository<ChatMessage, Long> {

    Page<ChatMessage> findByChatRoomIdAndIsDeletedFalseOrderByCreatedAtDesc(Long chatRoomId, Pageable pageable);

    @Query("SELECT cm FROM ChatMessage cm " +
           "WHERE cm.chatRoomId = :chatRoomId " +
           "AND cm.isDeleted = false " +
           "ORDER BY cm.createdAt DESC")
    Page<ChatMessage> findMessagesByChatRoomId(@Param("chatRoomId") Long chatRoomId, Pageable pageable);

    @Query("SELECT cm FROM ChatMessage cm " +
           "WHERE cm.chatRoomId = :chatRoomId " +
           "AND cm.isDeleted = false " +
           "ORDER BY cm.createdAt DESC " +
           "LIMIT 1")
    Optional<ChatMessage> findLastMessageByChatRoomId(@Param("chatRoomId") Long chatRoomId);

    @Query("SELECT COUNT(cm) FROM ChatMessage cm " +
           "JOIN ChatParticipant cp ON cp.chatRoomId = cm.chatRoomId " +
           "WHERE cm.chatRoomId = :chatRoomId " +
           "AND cp.userId = :userId " +
           "AND cm.createdAt > COALESCE(cp.lastReadAt, :defaultDate) " +
           "AND cm.isDeleted = false")
    Long countUnreadMessages(@Param("chatRoomId") Long chatRoomId, 
                            @Param("userId") Long userId, 
                            @Param("defaultDate") OffsetDateTime defaultDate);
}
