package tndung.vnfb.smm.entity;

import lombok.*;
import org.hibernate.annotations.Filter;
import org.hibernate.annotations.FilterDef;
import org.hibernate.annotations.ParamDef;
import org.hibernate.annotations.Where;
import tndung.vnfb.smm.constant.enums.ChatRoomType;
import tndung.vnfb.smm.entity.audit.AbstractTenantEntity;

import javax.persistence.*;
import java.util.List;

@Entity
@Table(name = "chat_room")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FilterDef(name = "tenantFilter", parameters = { @ParamDef(name = "tenantId", type = "string") ,  @ParamDef(name = "wildcardTenant", type = "string") })
@Filter(name = "tenantFilter", condition = "(tenant_id = :tenantId  OR tenant_id = :wildcardTenant)")
@Where(clause = "is_active = true")
public class ChatRoom extends AbstractTenantEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name")
    private String name;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false)
    private ChatRoomType type = ChatRoomType.DIRECT;

    @Column(name = "created_by", nullable = false)
    private Long createdBy;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @OneToMany(mappedBy = "chatRoom", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Where(clause = "is_active = true")
    private List<ChatParticipant> participants;

    @OneToMany(mappedBy = "chatRoom", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @OrderBy("createdAt DESC")
    @Where(clause = "is_deleted = false")
    private List<ChatMessage> messages;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "created_by", insertable = false, updatable = false)
    private GUser creator;
}
