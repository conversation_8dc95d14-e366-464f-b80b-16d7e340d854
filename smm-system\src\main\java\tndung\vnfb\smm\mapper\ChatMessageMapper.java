package tndung.vnfb.smm.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import tndung.vnfb.smm.dto.chat.ChatMessageReq;
import tndung.vnfb.smm.dto.chat.ChatMessageRes;
import tndung.vnfb.smm.entity.ChatMessage;

@Mapper(componentModel = "spring", 
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        uses = {GUserMapper.class})
public interface ChatMessageMapper {

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "senderId", ignore = true)
    @Mapping(target = "isDeleted", constant = "false")
    @Mapping(target = "chatRoom", ignore = true)
    @Mapping(target = "sender", ignore = true)
    ChatMessage toEntity(ChatMessageReq req);

    ChatMessageRes toDTO(ChatMessage entity);
}
